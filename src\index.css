
@import url('https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600;700&family=Open+Sans:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light Mode Colors - New Color Scheme */
    --background: 0 0% 100%;       /* #FFFFFF - Crisp White */
    --foreground: 215 25% 27%;     /* #334155 - Slate Blue */

    --card: 210 33% 98%;           /* #F7F9FA - Light Gray */
    --card-foreground: 215 25% 27%; /* #334155 - Slate Blue */

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 27%;

    --primary: 215 25% 27%;        /* #334155 - Slate Blue */
    --primary-foreground: 0 0% 100%;

    --secondary: 210 33% 98%;      /* #F7F9FA - Light Gray */
    --secondary-foreground: 215 25% 27%;

    --muted: 210 33% 98%;
    --muted-foreground: 215 20% 47%; /* #64748B - <PERSON> Gray */

    --accent: 199 89% 60%;         /* #38BDF8 - <PERSON> Blue */
    --accent-foreground: 215 25% 27%;

    --destructive: 38 92% 50%;     /* #F59E0B - Warm Orange */
    --destructive-foreground: 0 0% 100%;

    --border: 214 32% 91%;         /* #E2E8F0 - Faint Blue-Gray */
    --input: 214 32% 91%;
    --ring: 222 47% 33%;           /* #1E3A8A - Navy Blue */

    --radius: 0.75rem;

    /* Weather-related colors */
    --sky: 199 89% 60%;            /* #38BDF8 - Sky Blue */
    --sky-hover: 222 47% 33%;      /* #1E3A8A - Navy Blue */

    --orange: 38 92% 50%;          /* #F59E0B - Warm Orange */
    --orange-hover: 38 92% 45%;

    --cyan: 171 72% 50%;           /* #2DD4BF - Soft Teal */
    --cyan-hover: 171 72% 45%;

    --yellow: 38 92% 50%;          /* #F59E0B - Warm Orange */
    --yellow-hover: 38 92% 45%;

    --green: 156 64% 52%;          /* #34D399 - Light Green */
    --green-hover: 156 64% 47%;
  }

  .dark {
    /* Keep dark mode unchanged */
    --background: 217 33% 19.5%; /* #1E2A44 */
    --foreground: 210 20% 92%;   /* #E2E8F0 */

    --card: 217 33% 25%;
    --card-foreground: 210 20% 92%;

    --popover: 217 33% 19.5%;
    --popover-foreground: 210 20% 92%;

    --primary: 210 20% 92%;      /* #E2E8F0 */
    --primary-foreground: 217 33% 19.5%;

    --secondary: 217.2 32.6% 26%;
    --secondary-foreground: 210 20% 92%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 75.1%;

    --accent: 217.2 32.6% 27.5%;
    --accent-foreground: 210 20% 92%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 217.2 32.6% 27.5%;
    --input: 217.2 32.6% 27.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html, body, #root {
    @apply h-full;
  }

  body {
    @apply bg-background text-foreground font-opensans;
    font-size: 16px;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-nunito font-bold;
    font-size: 24px;
  }

  .glass-panel {
    @apply bg-card border border-border/50 dark:bg-black/20 dark:backdrop-blur-sm dark:border-white/10 rounded-lg shadow-[0_2px_4px_rgba(0,0,0,0.05)] dark:shadow-lg transition-all duration-300 hover:shadow-lg hover:border-sky/30 dark:hover:border-cyan/30;
  }

  /* Animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
  }

  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .animate-slide-in {
    animation: slideIn 0.5s ease-out;
  }

  .animate-pulse-slow {
    animation: pulse 2s infinite;
  }

  /* Hover effects */
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  /* Loading spinner */
  .loading-spinner {
    border: 2px solid rgba(59, 130, 246, 0.1);
    border-left: 2px solid rgb(59, 130, 246);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-md hover:-translate-y-1;
  }

  .weather-icon {
    @apply text-sky dark:text-cyan;
  }

  .weather-icon-sun {
    @apply text-orange dark:text-yellow;
  }

  .weather-icon-rain {
    @apply text-green;
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-transparent;
}

::-webkit-scrollbar-thumb {
  @apply bg-sky/30 dark:bg-cyan/30 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-sky/50 dark:bg-cyan/50;
}
