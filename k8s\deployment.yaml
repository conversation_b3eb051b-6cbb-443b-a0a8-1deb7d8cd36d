apiVersion: v1
kind: Secret
metadata:
  name: atmoswift-secrets
type: Opaque
data:
  openweather-api-key: ${OPENWEATHER_API_KEY_BASE64}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: atmoswift-backend
  labels:
    app: atmoswift-backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: atmoswift-backend
  template:
    metadata:
      labels:
        app: atmoswift-backend
    spec:
      containers:
      - name: backend
        image: yourusername/atmoswift-backend:latest
        ports:
        - containerPort: 3000
        env:
        - name: OPENWEATHER_API_KEY
          valueFrom:
            secretKeyRef:
              name: atmoswift-secrets
              key: openweather-api-key
        resources:
          limits:
            cpu: "500m"
            memory: "512Mi"
          requests:
            cpu: "200m"
            memory: "256Mi"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: atmoswift-frontend
  labels:
    app: atmoswift-frontend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: atmoswift-frontend
  template:
    metadata:
      labels:
        app: atmoswift-frontend
    spec:
      containers:
      - name: frontend
        image: yourusername/atmoswift-frontend:latest
        ports:
        - containerPort: 80
        resources:
          limits:
            cpu: "300m"
            memory: "256Mi"
          requests:
            cpu: "100m"
            memory: "128Mi"
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: atmoswift-backend
spec:
  selector:
    app: atmoswift-backend
  ports:
  - port: 3000
    targetPort: 3000
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: atmoswift-frontend
spec:
  selector:
    app: atmoswift-frontend
  ports:
  - port: 80
    targetPort: 80
  type: LoadBalancer
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: atmoswift-ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - weather.yourdomain.com
    secretName: atmoswift-tls
  rules:
  - host: weather.yourdomain.com
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: atmoswift-backend
            port:
              number: 3000
      - path: /
        pathType: Prefix
        backend:
          service:
            name: atmoswift-frontend
            port:
              number: 80
